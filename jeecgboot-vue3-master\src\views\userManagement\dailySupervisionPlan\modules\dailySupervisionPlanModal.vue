是<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="60%">
    <a-form ref="formRef" :model="orderMainModel" @submit="handleSubmit" :label-col="labelCol" :wrapper-col="wrapperCol"
      :rules="validatorRules">
      <a-button type="primary" @click="chooseProjectFun">
        选择检察人员
      </a-button>

      <a-row class="form-row" :gutter="8">
        <a-col :span="20">
          <a-form-item label="人员名称" name="userName">
            <a-input disabled v-model:value="orderMainModel.userName" placeholder="请输入" />
          </a-form-item>
        </a-col>

        <a-col :span="20">
          <a-form-item label="现场地址" name="place">
            <a-input v-model:value="orderMainModel.place" placeholder="请输入现场地址" />
          </a-form-item>
        </a-col>

        <a-col :span="20">
          <a-form-item label="监察内容" name="monitorContent">
            <a-textarea v-model:value="orderMainModel.monitorContent" placeholder="请输入监察内容" :rows="3" />
          </a-form-item>
        </a-col>

        <a-col :span="20">
          <a-form-item label="监察纠正措施" name="correctiveAction">
            <a-textarea v-model:value="orderMainModel.correctiveAction" placeholder="请输入监察纠正措施" :rows="3" />
          </a-form-item>
        </a-col>


      </a-row>
    </a-form>
    <ChooseAnnualSupervisionPlanPeopleModal @register="chooseProjectModal" @success="handleChooseProjectReturn"></ChooseAnnualSupervisionPlanPeopleModal>

  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form/index';
import { getToken } from '/@/utils/auth';
import { uploadUrl } from '/@/api/common/api';
import { formSchema } from '../dailySupervisionPlan.data';
import { saveOrUpdate } from '../dailySupervisionPlan.api';
import ChooseAnnualSupervisionPlanPeopleModal from './ChooseAnnualSupervisionPlanPeopleModal.vue'
import { MinusCircleOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { message, Upload, Form } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';

// 移除了不必要的类型别名声明

// Emits声明
const emit = defineEmits(['register', 'success']);
const formRef = ref();
const isUpdate = ref(true);
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 5 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 16 },
});

interface RecordFile {
  id: string;
  requireVal: string;
  fileList: any[];
}

const orderMainModel = reactive<any>({
  id: null,
});
const headers = reactive({
  'X-Access-Token': getToken(),
});
let site = ref(0);
let fileList = ref([]);
const validatorRules = {
  userName: [{ required: true, message: '人员名称必填！' }],
};

//表单赋值
const [chooseProjectModal, { openModal: openChooseProjectModal }] = useModal();
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  formRef.value?.resetFields();
  reset();
  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  if (unref(isUpdate)) {

    Object.assign(orderMainModel, data.record);
  } else {
  }
});
//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
//表单提交事件
function handleSubmit(v) {
  formRef.value
    ?.validate()
    .then(async () => {
      try {
        // let values = await validate();
        setModalProps({ confirmLoading: true });
        //提交表单
        await saveOrUpdate(orderMainModel, isUpdate.value);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, orderMainModel });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: any) => {
      console.log('error', error);
    });
}
function reset() {
  orderMainModel.id = null;
  
}


function chooseProjectFun() {
  openChooseProjectModal(true, {})
}
function handleChooseProjectReturn(value) {
  console.log("🚀 ~ handleChooseProjectReturn ~ value:", value)
  orderMainModel.planId=value[0].id
  orderMainModel.userName=value[0].userName
}


</script>

<style lang="less" scoped>
.fontColor {
  color: black;
}
</style>