<template>
<BasicModal v-bind="$attrs" :width="1000"
    @register="regSelTemplateModal"
    :afterClose="onAfterClose"
    title="选择人员"
    @ok="handleOk">
    
    <BasicTable @register="regiTable" rowKey="id" :rowSelection="{ type: 'radio' }"></BasicTable>
    
</BasicModal>
</template>

<script lang="ts" name="ReagentSelectRadio" setup>
import { reactive,ref } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { BasicTable, BasicColumn, FormSchema, useTable } from '/@/components/Table';
import { useMessage } from '/@/hooks/web/useMessage';

const { createMessage } = useMessage();
const emit = defineEmits(['ok']);

enum Api {
  list= "/lims/employee/getExamInfoList",
}
const SelectTemModel = reactive({
    index: -1
});

const [regSelTemplateModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
console.log("🚀 ~ file: SelectMethodRadioModal.vue:28 ~ const[regSelTemplateModal,{setModalProps,closeModal}]=useModalInner ~ data:", data)
    Object.assign(SelectTemModel, JSON.parse(JSON.stringify(data)))
    console.log("🚀 ~ file: SelectMeth]=useModalInner ~ methodModel:", SelectTemModel)
})

const queryParam: FormSchema[]=[
    {
        label: '计划内容',
        field: 'sopName',
        component: 'JInput',
        colProps: { span: 8 },
    },
    {
        label: '名字',
        field: 'isFinish',
        component: 'Select',
        componentProps: {
            options: [
                {
                    label: '是',
                    value: '1',
                },
                {
                    label: '否',
                    value: '0',
                },
            ],
        },
        colProps: { span: 8 },
    },
];

const columns:BasicColumn[]=[
    {
        title: '计划内容',
        dataIndex: 'sopName',
        align: 'center',
        width: 120,
    },
    {
        title: '指派人员',
        dataIndex: 'assignPeopleName',
        align: 'center',
        width: 100,
    },
    {
        title: '制定时间',
        dataIndex: 'createTime',
        align: 'center',
        width: 100,
    },
    
]
//注册table数据
// const { tableContext } = useListPage({
//     tableProps: {
//         title: '基础信息',
//         api: (params) => defHttp.get({url: Api.list, params}),
//         columns,
//         rowKey: 'id',
//         canResize: false,
//         formConfig: {
//             labelCol: { span:8 },
//             wrapperCol: { span:24 },
//             schemas: queryParam,
//             fieldMapToTime: [],
//         },
//     },
// });
const [regiTable, { 
    getSelectRows,clearSelectedRowKeys
}] = useTable({
    title: '基础信息',
    api: (params) => defHttp.get({url: Api.list, params}),
    columns,
    rowKey: 'id',
    canResize: false,
    useSearchForm: true,
    formConfig: {
        labelCol: { span:8 },
        wrapperCol: { span:24 },
        schemas: queryParam,
        fieldMapToTime: [],
    },
    showTableSetting: false,
});

function handleOk(){
    const selected=JSON.parse(JSON.stringify(getSelectRows()))
    console.log("🚀 ~ file: SelectConfigurationRadioModal.vue:103 ~ handleOk ~ selected:", selected)
    if(selected.length>0){
        let stModel = SelectTemModel;
        emit('success', selected,stModel)
        closeModal()
    }else{
        createMessage.warning('请至少选择一项')
    }
}

function onAfterClose(){
    clearSelectedRowKeys();
    console.log('onAfterClose');
}

</script>

<style>
</style>